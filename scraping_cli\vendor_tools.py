"""
Vendor-Specific Tool Extensions

Provides specialized tool extensions for Tesco, Asda, and Costco that adapt
core browser tools for each vendor's unique site structure, navigation patterns,
and anti-bot requirements.
"""

import asyncio
import logging
import time
import random
from typing import List, Optional, Dict, Any, Union
from dataclasses import dataclass
from datetime import datetime

from pydantic import BaseModel, Field

from .browser_tools import (
    BrowserbaseTool, NavigationTool, InteractionTool, ExtractionTool,
    ScreenshotTool, WaitingTool, AntiBotConfig, BrowserbaseToolInput
)
from .browserbase_manager import BrowserbaseManager


@dataclass
class VendorConfig:
    """Configuration for vendor-specific behavior."""
    name: str
    base_url: str
    search_selectors: List[str]
    product_card_selectors: List[str]
    product_title_selectors: List[str]
    product_price_selectors: List[str]
    product_image_selectors: List[str]
    pagination_selectors: List[str]
    category_selectors: List[str]
    anti_bot_delays: Dict[str, tuple]  # (min_delay, max_delay) for different operations
    session_timeout: int = 30000
    max_retries: int = 3
    enable_stealth: bool = True


class VendorTool(BrowserbaseTool):
    """
    Base class for vendor-specific tools with common vendor behavior.
    """
    
    def __init__(self, 
                 browser_manager: BrowserbaseManager,
                 vendor_config: VendorConfig,
                 anti_bot_config: Optional[AntiBotConfig] = None,
                 **kwargs):
        super().__init__(browser_manager, anti_bot_config, **kwargs)
        self.vendor_config = vendor_config
        self.logger = logging.getLogger(f"{__name__}.{vendor_config.name}")
    
    async def _apply_vendor_specific_delays(self, operation: str) -> None:
        """Apply vendor-specific delays for different operations."""
        if operation in self.vendor_config.anti_bot_delays:
            min_delay, max_delay = self.vendor_config.anti_bot_delays[operation]
            delay = random.uniform(min_delay, max_delay)
            self.logger.debug(f"Applying {self.vendor_config.name} delay for {operation}: {delay:.2f}s")
            await asyncio.sleep(delay)
    
    async def _retry_operation(self, operation_func, *args, max_retries: int = None, **kwargs):
        """Retry an operation with vendor-specific error handling."""
        max_retries = max_retries or self.vendor_config.max_retries
        
        for attempt in range(max_retries):
            try:
                return await operation_func(*args, **kwargs)
            except Exception as e:
                self.logger.warning(f"{self.vendor_config.name} operation failed (attempt {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(random.uniform(1, 3))
                else:
                    raise e


class TescoTool(VendorTool):
    """
    Tesco-specific tool extensions with Tesco's navigation patterns and anti-bot measures.
    """
    
    def __init__(self, browser_manager: BrowserbaseManager, **kwargs):
        vendor_config = VendorConfig(
            name="Tesco",
            base_url="https://www.tesco.com",
            search_selectors=[
                "input[data-testid='search-input']",
                "input[name='search']",
                "#search-input"
            ],
            product_card_selectors=[
                ".product-list-grid .product-card",
                "[data-testid='product-card']",
                ".product-item"
            ],
            product_title_selectors=[
                "h1[data-testid='product-title']",
                ".product-title",
                "h1"
            ],
            product_price_selectors=[
                "span.price",
                "[data-testid='product-price']",
                ".price"
            ],
            product_image_selectors=[
                "img[data-testid='product-image']",
                ".product-image img",
                "img"
            ],
            pagination_selectors=[
                ".pagination-next",
                "[data-testid='pagination-next']",
                ".next-page"
            ],
            category_selectors=[
                "nav[aria-label='Main menu']",
                ".category-menu",
                ".main-navigation"
            ],
            anti_bot_delays={
                'navigation': (2, 5),
                'interaction': (1, 3),
                'extraction': (0.5, 2),
                'search': (1, 4)
            }
        )
        super().__init__(browser_manager, vendor_config, **kwargs)
    
    async def navigate_to_category(self, category_name: str) -> Dict[str, Any]:
        """Navigate to a Tesco category using mega menu navigation."""
        try:
            # Apply Tesco-specific delays
            await self._apply_vendor_specific_delays('navigation')
            
            # Navigate to homepage first
            await self._retry_operation(
                self.browser_operations.navigate,
                url=self.vendor_config.base_url,
                wait_until="networkidle"
            )
            
            # Wait for main navigation to load
            await self.browser_operations.wait_for_element(
                selector=self.vendor_config.category_selectors[0],
                timeout=10000
            )
            
            # Click on category menu
            await self._retry_operation(
                self.browser_operations.click,
                selector="nav[aria-label='Main menu']",
                timeout=5000
            )
            
            # Find and click the specific category
            category_selector = f"a[href*='{category_name.lower()}'], a:contains('{category_name}')"
            await self._retry_operation(
                self.browser_operations.click,
                selector=category_selector,
                timeout=5000
            )
            
            # Wait for product grid to load
            await self.browser_operations.wait_for_element(
                selector=self.vendor_config.product_card_selectors[0],
                timeout=15000
            )
            
            return {
                'status': 'success',
                'category': category_name,
                'url': self.browser_operations.get_current_url()
            }
            
        except Exception as e:
            return await self._handle_error(e, f"tesco_category_navigation_{category_name}")
    
    async def search_products(self, query: str) -> Dict[str, Any]:
        """Search for products on Tesco with proper delays and error handling."""
        try:
            await self._apply_vendor_specific_delays('search')
            
            # Navigate to homepage
            await self._retry_operation(
                self.browser_operations.navigate,
                url=self.vendor_config.base_url,
                wait_until="networkidle"
            )
            
            # Find and fill search input
            search_input = None
            for selector in self.vendor_config.search_selectors:
                try:
                    await self.browser_operations.wait_for_element(selector, timeout=5000)
                    search_input = selector
                    break
                except:
                    continue
            
            if not search_input:
                raise ValueError("Could not find search input on Tesco")
            
            # Type search query
            await self._retry_operation(
                self.browser_operations.type_text,
                selector=search_input,
                text=query,
                timeout=5000
            )
            
            # Submit search
            await self._retry_operation(
                self.browser_operations.click,
                selector="button[type='submit'], .search-button",
                timeout=5000
            )
            
            # Wait for results
            await self.browser_operations.wait_for_element(
                selector=self.vendor_config.product_card_selectors[0],
                timeout=15000
            )
            
            return {
                'status': 'success',
                'query': query,
                'url': self.browser_operations.get_current_url()
            }
            
        except Exception as e:
            return await self._handle_error(e, f"tesco_search_{query}")
    
    async def extract_product_cards(self) -> List[Dict[str, Any]]:
        """Extract product cards from Tesco product listing pages."""
        try:
            await self._apply_vendor_specific_delays('extraction')
            
            products = []
            
            # Find all product cards
            for card_selector in self.vendor_config.product_card_selectors:
                try:
                    cards = await self.browser_operations.find_elements(card_selector)
                    if cards:
                        break
                except:
                    continue
            
            if not cards:
                return []
            
            # Extract data from each card
            for card in cards:
                try:
                    # Extract title
                    title = None
                    for title_selector in self.vendor_config.product_title_selectors:
                        try:
                            title_elem = await card.find_element(title_selector)
                            title = await title_elem.text()
                            break
                        except:
                            continue
                    
                    # Extract price
                    price = None
                    for price_selector in self.vendor_config.product_price_selectors:
                        try:
                            price_elem = await card.find_element(price_selector)
                            price = await price_elem.text()
                            break
                        except:
                            continue
                    
                    # Extract image
                    image = None
                    for image_selector in self.vendor_config.product_image_selectors:
                        try:
                            img_elem = await card.find_element(image_selector)
                            image = await img_elem.get_attribute('src')
                            break
                        except:
                            continue
                    
                    # Extract link
                    link = None
                    try:
                        link_elem = await card.find_element('a')
                        link = await link_elem.get_attribute('href')
                    except:
                        pass
                    
                    if title or price:  # Only include if we have at least title or price
                        products.append({
                            'title': title,
                            'price': price,
                            'image': image,
                            'link': link,
                            'vendor': 'Tesco'
                        })
                
                except Exception as e:
                    self.logger.warning(f"Failed to extract product card: {e}")
                    continue
            
            return products
            
        except Exception as e:
            self.logger.error(f"Failed to extract Tesco product cards: {e}")
            return []


class AsdaTool(VendorTool):
    """
    Asda-specific tool extensions with Asda's navigation patterns and anti-bot measures.
    """
    
    def __init__(self, browser_manager: BrowserbaseManager, **kwargs):
        vendor_config = VendorConfig(
            name="Asda",
            base_url="https://groceries.asda.com",
            search_selectors=[
                "input[data-testid='search-input']",
                "input[name='search']",
                "#search-input"
            ],
            product_card_selectors=[
                ".product-list .product-item",
                "[data-testid='product-card']",
                ".product-card"
            ],
            product_title_selectors=[
                "h1[data-testid='product-title']",
                ".product-title",
                "h1"
            ],
            product_price_selectors=[
                "span.price",
                "[data-testid='product-price']",
                ".price"
            ],
            product_image_selectors=[
                "img[data-testid='product-image']",
                ".product-image img",
                "img"
            ],
            pagination_selectors=[
                ".pagination-next",
                "[data-testid='pagination-next']",
                ".next-page"
            ],
            category_selectors=[
                ".category-menu",
                ".main-navigation",
                "nav"
            ],
            anti_bot_delays={
                'navigation': (1.5, 4),
                'interaction': (0.8, 2.5),
                'extraction': (0.3, 1.5),
                'search': (0.8, 3)
            }
        )
        super().__init__(browser_manager, vendor_config, **kwargs)
    
    async def navigate_to_category(self, category_name: str) -> Dict[str, Any]:
        """Navigate to an Asda category using breadcrumb navigation."""
        try:
            await self._apply_vendor_specific_delays('navigation')
            
            # Navigate to homepage
            await self._retry_operation(
                self.browser_operations.navigate,
                url=self.vendor_config.base_url,
                wait_until="networkidle"
            )
            
            # Wait for navigation to load
            await self.browser_operations.wait_for_element(
                selector=self.vendor_config.category_selectors[0],
                timeout=10000
            )
            
            # Use breadcrumb navigation for Asda
            category_selector = f"a[href*='{category_name.lower()}'], a:contains('{category_name}')"
            await self._retry_operation(
                self.browser_operations.click,
                selector=category_selector,
                timeout=5000
            )
            
            # Wait for product grid
            await self.browser_operations.wait_for_element(
                selector=self.vendor_config.product_card_selectors[0],
                timeout=15000
            )
            
            return {
                'status': 'success',
                'category': category_name,
                'url': self.browser_operations.get_current_url()
            }
            
        except Exception as e:
            return await self._handle_error(e, f"asda_category_navigation_{category_name}")
    
    async def search_products(self, query: str) -> Dict[str, Any]:
        """Search for products on Asda with proper delays."""
        try:
            await self._apply_vendor_specific_delays('search')
            
            # Navigate to homepage
            await self._retry_operation(
                self.browser_operations.navigate,
                url=self.vendor_config.base_url,
                wait_until="networkidle"
            )
            
            # Find search input
            search_input = None
            for selector in self.vendor_config.search_selectors:
                try:
                    await self.browser_operations.wait_for_element(selector, timeout=5000)
                    search_input = selector
                    break
                except:
                    continue
            
            if not search_input:
                raise ValueError("Could not find search input on Asda")
            
            # Type search query
            await self._retry_operation(
                self.browser_operations.type_text,
                selector=search_input,
                text=query,
                timeout=5000
            )
            
            # Submit search
            await self._retry_operation(
                self.browser_operations.click,
                selector="button[type='submit'], .search-button",
                timeout=5000
            )
            
            # Wait for results
            await self.browser_operations.wait_for_element(
                selector=self.vendor_config.product_card_selectors[0],
                timeout=15000
            )
            
            return {
                'status': 'success',
                'query': query,
                'url': self.browser_operations.get_current_url()
            }
            
        except Exception as e:
            return await self._handle_error(e, f"asda_search_{query}")
    
    async def extract_product_cards(self) -> List[Dict[str, Any]]:
        """Extract product cards from Asda product listing pages."""
        try:
            await self._apply_vendor_specific_delays('extraction')
            
            products = []
            
            # Find product cards
            for card_selector in self.vendor_config.product_card_selectors:
                try:
                    cards = await self.browser_operations.find_elements(card_selector)
                    if cards:
                        break
                except:
                    continue
            
            if not cards:
                return []
            
            # Extract data from each card
            for card in cards:
                try:
                    # Extract title
                    title = None
                    for title_selector in self.vendor_config.product_title_selectors:
                        try:
                            title_elem = await card.find_element(title_selector)
                            title = await title_elem.text()
                            break
                        except:
                            continue
                    
                    # Extract price
                    price = None
                    for price_selector in self.vendor_config.product_price_selectors:
                        try:
                            price_elem = await card.find_element(price_selector)
                            price = await price_elem.text()
                            break
                        except:
                            continue
                    
                    # Extract image
                    image = None
                    for image_selector in self.vendor_config.product_image_selectors:
                        try:
                            img_elem = await card.find_element(image_selector)
                            image = await img_elem.get_attribute('src')
                            break
                        except:
                            continue
                    
                    # Extract link
                    link = None
                    try:
                        link_elem = await card.find_element('a')
                        link = await link_elem.get_attribute('href')
                    except:
                        pass
                    
                    if title or price:
                        products.append({
                            'title': title,
                            'price': price,
                            'image': image,
                            'link': link,
                            'vendor': 'Asda'
                        })
                
                except Exception as e:
                    self.logger.warning(f"Failed to extract Asda product card: {e}")
                    continue
            
            return products
            
        except Exception as e:
            self.logger.error(f"Failed to extract Asda product cards: {e}")
            return []


class CostcoTool(VendorTool):
    """
    Costco-specific tool extensions with robust error handling for Costco's unreliable site.
    """
    
    def __init__(self, browser_manager: BrowserbaseManager, **kwargs):
        vendor_config = VendorConfig(
            name="Costco",
            base_url="https://www.costco.co.uk",
            search_selectors=[
                "input[type='search']",
                "input[name='search']",
                "#search-input"
            ],
            product_card_selectors=[
                ".product-item",
                ".product-card",
                "[data-testid='product-card']"
            ],
            product_title_selectors=[
                "h1",
                ".product-title",
                "[data-testid='product-title']"
            ],
            product_price_selectors=[
                ".price",
                "[data-testid='product-price']",
                "span.price"
            ],
            product_image_selectors=[
                "img",
                ".product-image img",
                "[data-testid='product-image']"
            ],
            pagination_selectors=[
                ".pagination-next",
                ".next-page",
                "[data-testid='pagination-next']"
            ],
            category_selectors=[
                ".category-menu",
                ".main-navigation",
                "nav"
            ],
            anti_bot_delays={
                'navigation': (3, 8),  # Longer delays for Costco's unreliable site
                'interaction': (2, 5),
                'extraction': (1, 3),
                'search': (2, 6)
            },
            max_retries=5  # More retries for Costco
        )
        super().__init__(browser_manager, vendor_config, **kwargs)
    
    async def navigate_to_category(self, category_name: str) -> Dict[str, Any]:
        """Navigate to a Costco category with robust error handling."""
        try:
            await self._apply_vendor_specific_delays('navigation')
            
            # Navigate to homepage with retry logic
            await self._retry_operation(
                self.browser_operations.navigate,
                url=self.vendor_config.base_url,
                wait_until="networkidle"
            )
            
            # Wait for navigation with longer timeout
            await self.browser_operations.wait_for_element(
                selector=self.vendor_config.category_selectors[0],
                timeout=20000  # Longer timeout for Costco
            )
            
            # Find and click category
            category_selector = f"a[href*='{category_name.lower()}'], a:contains('{category_name}')"
            await self._retry_operation(
                self.browser_operations.click,
                selector=category_selector,
                timeout=10000
            )
            
            # Wait for product grid with longer timeout
            await self.browser_operations.wait_for_element(
                selector=self.vendor_config.product_card_selectors[0],
                timeout=25000
            )
            
            return {
                'status': 'success',
                'category': category_name,
                'url': self.browser_operations.get_current_url()
            }
            
        except Exception as e:
            return await self._handle_error(e, f"costco_category_navigation_{category_name}")
    
    async def search_products(self, query: str) -> Dict[str, Any]:
        """Search for products on Costco with robust error handling."""
        try:
            await self._apply_vendor_specific_delays('search')
            
            # Navigate to homepage
            await self._retry_operation(
                self.browser_operations.navigate,
                url=self.vendor_config.base_url,
                wait_until="networkidle"
            )
            
            # Find search input
            search_input = None
            for selector in self.vendor_config.search_selectors:
                try:
                    await self.browser_operations.wait_for_element(selector, timeout=10000)
                    search_input = selector
                    break
                except:
                    continue
            
            if not search_input:
                raise ValueError("Could not find search input on Costco")
            
            # Type search query
            await self._retry_operation(
                self.browser_operations.type_text,
                selector=search_input,
                text=query,
                timeout=10000
            )
            
            # Submit search
            await self._retry_operation(
                self.browser_operations.click,
                selector="button[type='submit'], .search-button",
                timeout=10000
            )
            
            # Wait for results with longer timeout
            await self.browser_operations.wait_for_element(
                selector=self.vendor_config.product_card_selectors[0],
                timeout=25000
            )
            
            return {
                'status': 'success',
                'query': query,
                'url': self.browser_operations.get_current_url()
            }
            
        except Exception as e:
            return await self._handle_error(e, f"costco_search_{query}")
    
    async def extract_product_cards(self) -> List[Dict[str, Any]]:
        """Extract product cards from Costco with robust error handling."""
        try:
            await self._apply_vendor_specific_delays('extraction')
            
            products = []
            
            # Find product cards with multiple attempts
            cards = []
            for card_selector in self.vendor_config.product_card_selectors:
                try:
                    cards = await self.browser_operations.find_elements(card_selector)
                    if cards:
                        break
                except:
                    continue
            
            if not cards:
                self.logger.warning("No product cards found on Costco page")
                return []
            
            # Extract data from each card
            for card in cards:
                try:
                    # Extract title
                    title = None
                    for title_selector in self.vendor_config.product_title_selectors:
                        try:
                            title_elem = await card.find_element(title_selector)
                            title = await title_elem.text()
                            break
                        except:
                            continue
                    
                    # Extract price
                    price = None
                    for price_selector in self.vendor_config.product_price_selectors:
                        try:
                            price_elem = await card.find_element(price_selector)
                            price = await price_elem.text()
                            break
                        except:
                            continue
                    
                    # Extract image
                    image = None
                    for image_selector in self.vendor_config.product_image_selectors:
                        try:
                            img_elem = await card.find_element(image_selector)
                            image = await img_elem.get_attribute('src')
                            break
                        except:
                            continue
                    
                    # Extract link
                    link = None
                    try:
                        link_elem = await card.find_element('a')
                        link = await link_elem.get_attribute('href')
                    except:
                        pass
                    
                    if title or price:
                        products.append({
                            'title': title,
                            'price': price,
                            'image': image,
                            'link': link,
                            'vendor': 'Costco'
                        })
                
                except Exception as e:
                    self.logger.warning(f"Failed to extract Costco product card: {e}")
                    continue
            
            return products
            
        except Exception as e:
            self.logger.error(f"Failed to extract Costco product cards: {e}")
            return []


# Factory functions for creating vendor-specific tools
def create_tesco_tool(browser_manager: BrowserbaseManager, 
                     anti_bot_config: Optional[AntiBotConfig] = None) -> TescoTool:
    """Create a Tesco-specific tool instance."""
    return TescoTool(browser_manager, anti_bot_config=anti_bot_config)


def create_asda_tool(browser_manager: BrowserbaseManager,
                    anti_bot_config: Optional[AntiBotConfig] = None) -> AsdaTool:
    """Create an Asda-specific tool instance."""
    return AsdaTool(browser_manager, anti_bot_config=anti_bot_config)


def create_costco_tool(browser_manager: BrowserbaseManager,
                      anti_bot_config: Optional[AntiBotConfig] = None) -> CostcoTool:
    """Create a Costco-specific tool instance."""
    return CostcoTool(browser_manager, anti_bot_config=anti_bot_config)


def create_all_vendor_tools(browser_manager: BrowserbaseManager,
                           anti_bot_config: Optional[AntiBotConfig] = None) -> Dict[str, VendorTool]:
    """Create all vendor-specific tools with the same configuration."""
    return {
        'tesco': create_tesco_tool(browser_manager, anti_bot_config),
        'asda': create_asda_tool(browser_manager, anti_bot_config),
        'costco': create_costco_tool(browser_manager, anti_bot_config)
    } 